
.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 7vh;
  background-color: #233463;
  border-bottom: 2px solid #3b82f6;
  color: white;
}
.block-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: 100vh;
  width: 90%;
  margin: auto;
}

.block {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 60%;
  background-color: #1e293b;
  margin: 15px;
  border-radius: 8px;
}

.block-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 10%;
  width: 90%;
  margin: 10px;
  background-color: #233463;
  border-bottom: 2px solid #3b82f6;
  color: white;
}

.block-body {
  display: flex;
  flex-direction: column;
  height: 80%;
  width: 90%;
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 5px;
  margin: 20px;
  padding: 10px;
  box-sizing: border-box;
}

.block-body textarea {
  width: 100%;
  height: 100%;
  font-family: monospace;
  font-size: 1.2rem;
  resize: none;
  border: none;
  background-color: transparent;
  color: #e2e8f0;
  outline: none;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  flex: 1;
}

.block-body textarea::placeholder {
  color: #64748b;
}

.block-body > div {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: auto;
}

.block-body .button {
  display: flex;
  flex-direction: row;
  align-items: bottom;
  justify-content: flex-end;
  height: 10%;
  width: 100%;
  margin: 10px;
}