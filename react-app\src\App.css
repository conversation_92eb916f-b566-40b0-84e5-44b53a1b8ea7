
html, body {
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#root {
  height: 100vh;
  overflow: hidden;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 7vh;
  background: linear-gradient(135deg, #233463 0%, #1e3a8a 100%);
  border-bottom: 2px solid #3b82f6;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.block-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: 120vh;
  width: 90%;
  margin: auto;
}

.block {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 60%;
  background-color: #1e293b;
  margin: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #334155;
}

.block-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 10%;
  width: 90%;
  margin: 10px;
  background: linear-gradient(135deg, #233463 0%, #1e3a8a 100%);
  border-bottom: 2px solid #3b82f6;
  color: white;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.block-body {
  display: flex;
  flex-direction: column;
  height: 80%;
  width: 90%;
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 0 0 8px 8px;
  margin: 0 20px 20px 20px;
  padding: 16px;
  box-sizing: border-box;
}

.block-body textarea {
  width: 100%;
  height: 100%;
  font-family: monospace;
  font-size: 1.2rem;
  resize: none;
  border: none;
  background-color: transparent;
  color: #e2e8f0;
  outline: none;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.block-body textarea::placeholder {
  color: #64748b;
}

.block-body > div {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.block-body .button {
  display: flex;
  flex-direction: row;
  align-items: bottom;
  justify-content: flex-end;
  height: 70px;
}

button {
  margin: 10px;
  background-color: #1e293b;
  border: 1px solid #334155;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 32px;
}

button svg {
  width: 16px;
  height: 16px;
}

button:hover {
  background-color: #1a2332;
}

button:active {
  background-color: #334155;
}
