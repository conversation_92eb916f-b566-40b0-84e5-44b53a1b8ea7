html, body {
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#root {
  height: 100vh;
  overflow: hidden;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 10vh;
  background: linear-gradient(135deg, #324372 0%, #192850 100%);
  border-bottom: 2px solid #3961a1;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.block-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  height: 140vh;
  width: 90%;
  margin: auto;
}

.block {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 60%;
  background-color: #1e293b;
  margin: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #334155;
}

.block-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 10%;
  width: 90%;
  margin: 10px;
  background: linear-gradient(135deg, #324372 0%, #192850 100%);
  border-bottom: 2px solid #3961a1;
  color: white;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.block-body {
  display: flex;
  flex-direction: column;
  height: 80%;
  width: 90%;
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 0 0 8px 8px;
  margin: 0 20px 20px 20px;
  padding: 16px;
  box-sizing: border-box;
}

.block-body .input textarea {
  width: 100%;
  height: 100%;
  font-family: monospace;
  font-size: 1.2rem;
  resize: none;
  border: none;
  background-color: transparent;
  color: #e2e8f0;
  outline: none;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.block-body .input textarea::placeholder {
  color: #64748b;
}

/* Input Container */
.input-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
  padding: 8px;
}

/* Input Section */
.input-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #233463 0%, #1e3a8a 100%);
  border-radius: 6px;
  border-bottom: 2px solid #3b82f6;
}

.input-header h4 {
  margin: 0;
  color: white;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.input {
  flex: 1;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #334155;
}

/* Signature Section */
.signature-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 120px;
}

.signature-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border-radius: 6px;
  border: 1px solid #334155;
}

.signature-header h4 {
  margin: 0;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.optional-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signature-input {
  flex: 1;
}

.secret-key-input {
  width: 100%;
  height: 40px;
  padding: 10px 12px;
  background-color: #1e293b;
  border: 1px solid #334155;
  border-radius: 6px;
  color: #e2e8f0;
  font-size: 14px;
  font-family: monospace;
  outline: none;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.secret-key-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.secret-key-input::placeholder {
  color: #64748b;
  font-style: italic;
}

/* Verification Status */
.verification-status {
  padding: 8px 12px;
  background-color: #0f172a;
  border: 1px solid #334155;
  border-radius: 6px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  color: #f59e0b;
  font-size: 12px;
  font-weight: 500;
}

.block-body > div {
  width: 100%;
  height: 100%;
}

.block-body .button {
  display: flex;
  flex-direction: row;
  align-items: bottom;
  justify-content: flex-end;
  height: 70px;
}

button {
  background-color: #1e293b;
  margin: 10px;
  color: white;
  padding: 10px 14px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 36px;
}

button svg {
  width: 16px;
  height: 16px;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}
