import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"

function App() {
  const [encodedInput, setEncodedInput] = useState('');
  const [decodedOutput, setDecodedOutput] = useState('');

  // Function to decode JWT token
  const handleDecode = (input: string) => {
    try {
      if (input.trim() === '') {
        setDecodedOutput('');
        return;
      }

      // JWT tokens have 3 parts separated by dots
      const parts = input.split('.');
      if (parts.length !== 3) {
        setDecodedOutput('Invalid JWT format. JWT should have 3 parts separated by dots.');
        return;
      }

      // Decode header and payload (first two parts)
      const header = JSON.parse(atob(parts[0].replace(/-/g, '+').replace(/_/g, '/')));
      const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

      const decodedJWT = {
        header: header,
        payload: payload,
        signature: parts[2] // Keep signature as-is since it's encrypted
      };

      setDecodedOutput(JSON.stringify(decodedJWT, null, 2));
    } catch (error) {
      setDecodedOutput('Invalid JWT token or malformed JSON');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setEncodedInput(value);
    handleDecode(value);
  };

  const encodedInputBody = (
    <textarea
      value={encodedInput}
      onChange={handleInputChange}
      placeholder="Paste your JWT token here..."
    />
  );

  const decodedOutputBody = (
    <div style={{ 
      width: '100%',
      height: '100%',
      backgroundColor: '#242424',
      color: 'white',
      whiteSpace: 'pre-wrap',
      wordBreak: 'break-word'
    }}>
      {decodedOutput || 'Decoded output will appear here...'}
    </div>
  );

  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Enter Encoded Value" body={encodedInputBody} />
      <Block header="Decoded Output" body={decodedOutputBody} />
    </div>
    </>
  );
}

export default App
