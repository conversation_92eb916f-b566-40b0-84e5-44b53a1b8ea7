import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"

function App() {
  const [encodedInput, setEncodedInput] = useState('');
  const [decodedHeader, setDecodedHeader] = useState('');
  const [decodedPayload, setDecodedPayload] = useState('');
  const [decodedSignature, setDecodedSignature] = useState('');

  // Function to decode JWT token
  const handleDecode = (input: string) => {
    try {
      if (input.trim() === '') {
        setDecodedHeader('');
        setDecodedPayload('');
        setDecodedSignature('');
        return;
      }

      // JWT tokens have 3 parts
      const parts = input.split('.');
      if (parts.length !== 3) {
        setDecodedHeader('Invalid JWT format. JWT should have 3 parts separated by dots.');
        setDecodedPayload('');
        setDecodedSignature('');
        return;
      }

      // Decode header and payload (first two parts)
      const header = JSON.parse(atob(parts[0].replace(/-/g, '+').replace(/_/g, '/')));
      const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

      // Set individual sections
      setDecodedHeader(JSON.stringify(header, null, 2));
      setDecodedPayload(JSON.stringify(payload, null, 2));
      setDecodedSignature(parts[2]);

    } catch (error) {
      setDecodedHeader('Invalid JWT token or malformed JSON');
      setDecodedPayload('');
      setDecodedSignature('');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setEncodedInput(value);
    handleDecode(value);
  };

  const encodedInputBody = (
    <div className="input-container">
      <div className="input-section">
        <div className="input-header">
          <h4>JWT Token Input</h4>
        </div>
        <div className="input">
          <textarea
            value={encodedInput}
            onChange={handleInputChange}
            placeholder="Paste your JWT token here..."
          />
        </div>
      </div>

      <div className="signature-section">
        <div className="signature-header">
          <h4>Signature Verification</h4>
          <span className="optional-badge">Optional</span>
        </div>
        <div className="signature-input">
          <input
            type="password"
            placeholder="Enter secret key for verification..."
            className="secret-key-input"
          />
        </div>
        <div className="verification-status">
          <div className="status-indicator">
            <span className="status-icon">⚠</span>
            <span className="status-text">Enter secret key to verify</span>
          </div>
        </div>
      </div>
    </div>
  );

  const JWTSection = ({ title, content, copyText }: { title: string; content: string; copyText: string }) => {
    const [isCopied, setIsCopied] = useState(false);

    const handleCopy = async () => {
      if (copyText) {
        try {
          await navigator.clipboard.writeText(copyText);
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy text: ', err);
          const textArea = document.createElement('textarea');
          textArea.value = copyText;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        }
      }
    };

    return (
      <div style={{
        border: '1px solid #334155',
        borderRadius: '6px',
        marginBottom: '12px',
        backgroundColor: '#0f172a'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 12px',
          backgroundColor: '#1e293b',
          borderBottom: '1px solid #334155',
          borderRadius: '6px 6px 0 0'
        }}>
          <span style={{ fontWeight: '600', fontSize: '14px', color: '#60a5fa' }}>{title}</span>
          <button
            onClick={handleCopy}
            style={{
              background: ` #1e293b`,
              border: '1px solid #334155',
              color: '#e2e8f0',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              minWidth: '24px',
              minHeight: '30px'
            }}
          >
            {isCopied ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
              </svg>
            ) : (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              </svg>
            )}
          </button>
        </div>
        <div style={{
          padding: '12px',
          fontFamily: 'monospace',
          fontSize: '13px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          color: '#e2e8f0',
          maxHeight: '150px',
          overflowY: 'auto'
        }}>
          {content || `${title} will appear here...`}
        </div>
      </div>
    );
  };

  const decodedOutputBody = (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
      padding: '8px'
    }}>
      <JWTSection title="Header" content={decodedHeader} copyText={decodedHeader} />
      <JWTSection title="Payload" content={decodedPayload} copyText={decodedPayload} />
      <JWTSection title="Signature" content={decodedSignature} copyText={decodedSignature} />
    </div>
  );

  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Enter Encoded Value" body={encodedInputBody} />
      <Block header="Decoded Output" body={decodedOutputBody} />
    </div>
    </>
  );
}

export default App
