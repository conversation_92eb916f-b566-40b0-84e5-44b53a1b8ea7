import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"

function App() {
  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Enter Encoded Value" body="This is the content of the first block" />
      <Block header="Decoded Output" body="This is the content of the second block" />
    </div>
    </>
  );
}

export default App
