import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"

function App() {
  const [encodedInput, setEncodedInput] = useState('');
  const [decodedOutput, setDecodedOutput] = useState('');

  // Function to decode the input (example: Base64 decoding)
  const handleDecode = (input: string) => {
    try {
      if (input.trim() === '') {
        setDecodedOutput('');
        return;
      }
      // Example: Base64 decoding
      const decoded = atob(input);
      setDecodedOutput(decoded);
    } catch (error) {
      setDecodedOutput('Invalid encoded input');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setEncodedInput(value);
    handleDecode(value);
  };

  const encodedInputBody = (
    <textarea
      value={encodedInput}
      onChange={handleInputChange}
      placeholder="Enter your encoded text here..."
      rows={5}
      style={{ width: '100%', resize: 'vertical' }}
    />
  );

  const decodedOutputBody = (
    <div style={{ 
      width: '100%',
      height: '100%',
      color: black,
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      whiteSpace: 'pre-wrap',
      wordBreak: 'break-word'
    }}>
      {decodedOutput || 'Decoded output will appear here...'}
    </div>
  );

  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Enter Encoded Value" body={encodedInputBody} />
      <Block header="Decoded Output" body={decodedOutputBody} />
    </div>
    </>
  );
}

export default App
