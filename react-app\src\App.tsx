import { useState } from 'react'
import './App.css'
import Header from "./Header.tsx"
import Block from "./Block.tsx"
import * as jose from 'jose'

function App() {
  const [encodedInput, setEncodedInput] = useState('');
  const [decodedHeader, setDecodedHeader] = useState('');
  const [decodedPayload, setDecodedPayload] = useState('');
  const [decodedSignature, setDecodedSignature] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const [verificationStatus, setVerificationStatus] = useState<'none' | 'valid' | 'invalid' | 'missing-key' | 'unsupported'>('none');

  // Function to safely verify JWT token
  const verifyToken = async (token: string, secret: string) => {
    if (!secret.trim()) {
      setVerificationStatus('missing-key');
      return;
    }

    try {
      // Parse header to check algorithm
      const parts = token.split('.');
      const header = JSON.parse(atob(parts[0].replace(/-/g, '+').replace(/_/g, '/')));

      // Only support HMAC algorithms for safety
      if (!header.alg || !header.alg.startsWith('HS')) {
        setVerificationStatus('unsupported');
        return;
      }

      // Create secret key for verification
      const secretKeyBytes = new TextEncoder().encode(secret);
      const cryptoKey = await jose.importJWK({
        kty: 'oct',
        k: jose.base64url.encode(secretKeyBytes)
      });

      // Verify the token
      await jose.jwtVerify(token, cryptoKey);
      setVerificationStatus('valid');
    } catch (error) {
      setVerificationStatus('invalid');
    }
  };

  // Function to decode JWT token
  const handleDecode = (input: string) => {
    try {
      if (input.trim() === '') {
        setDecodedHeader('');
        setDecodedPayload('');
        setDecodedSignature('');
        setVerificationStatus('none');
        return;
      }

      // JWT tokens have 3 parts
      const parts = input.split('.');
      if (parts.length !== 3) {
        setDecodedHeader('Invalid JWT format. JWT should have 3 parts separated by dots.');
        setDecodedPayload('');
        setDecodedSignature('');
        setVerificationStatus('none');
        return;
      }

      // Decode header and payload (first two parts)
      const header = JSON.parse(atob(parts[0].replace(/-/g, '+').replace(/_/g, '/')));
      const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

      // Set individual sections
      setDecodedHeader(JSON.stringify(header, null, 2));
      setDecodedPayload(JSON.stringify(payload, null, 2));
      setDecodedSignature(parts[2]);

      // Verify token if secret key is provided
      if (secretKey.trim()) {
        verifyToken(input, secretKey);
      } else {
        setVerificationStatus('missing-key');
      }

    } catch (error) {
      setDecodedHeader('Invalid JWT token or malformed JSON');
      setDecodedPayload('');
      setDecodedSignature('');
      setVerificationStatus('none');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setEncodedInput(value);
    handleDecode(value);
  };

  const handleSecretKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSecretKey(value);
    if (encodedInput.trim()) {
      handleDecode(encodedInput);
    }
  };

  const getVerificationStatusDisplay = () => {
    switch (verificationStatus) {
      case 'valid':
        return <span style={{ color: '#10b981', fontWeight: '600' }}>✓ Signature Verified</span>;
      case 'invalid':
        return <span style={{ color: '#ef4444', fontWeight: '600' }}>✗ Invalid Signature</span>;
      case 'missing-key':
        return <span style={{ color: '#f59e0b', fontWeight: '600' }}>⚠ Enter Secret Key to Verify</span>;
      case 'unsupported':
        return <span style={{ color: '#f59e0b', fontWeight: '600' }}>⚠ Only HMAC algorithms supported</span>;
      default:
        return <span style={{ color: '#6b7280' }}>No verification performed</span>;
    }
  };

  const encodedInputBody = (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', gap: '12px' }}>
      <textarea
        value={encodedInput}
        onChange={handleInputChange}
        placeholder="Paste your JWT token here..."
        style={{ flex: 1, resize: 'none' }}
      />
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <input
          type="password"
          value={secretKey}
          onChange={handleSecretKeyChange}
          placeholder="Enter secret key for verification (optional)"
          style={{
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #334155',
            backgroundColor: '#1e293b',
            color: '#e2e8f0',
            fontSize: '14px'
          }}
        />
        <div style={{ fontSize: '12px', padding: '4px 0' }}>
          {getVerificationStatusDisplay()}
        </div>
      </div>
    </div>
  );

  const JWTSection = ({ title, content, copyText }: { title: string; content: string; copyText: string }) => {
    const [isCopied, setIsCopied] = useState(false);

    const handleCopy = async () => {
      if (copyText) {
        try {
          await navigator.clipboard.writeText(copyText);
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy text: ', err);
          const textArea = document.createElement('textarea');
          textArea.value = copyText;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        }
      }
    };

    return (
      <div style={{
        border: '1px solid #334155',
        borderRadius: '6px',
        marginBottom: '12px',
        backgroundColor: '#0f172a'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 12px',
          backgroundColor: '#1e293b',
          borderBottom: '1px solid #334155',
          borderRadius: '6px 6px 0 0'
        }}>
          <span style={{ fontWeight: '600', fontSize: '14px', color: '#60a5fa' }}>{title}</span>
          <button
            onClick={handleCopy}
            style={{
              background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
              border: '1px solid #60a5fa',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              minWidth: '24px',
              minHeight: '24px'
            }}
          >
            {isCopied ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
              </svg>
            ) : (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              </svg>
            )}
          </button>
        </div>
        <div style={{
          padding: '12px',
          fontFamily: 'monospace',
          fontSize: '13px',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          color: '#e2e8f0',
          maxHeight: '150px',
          overflowY: 'auto'
        }}>
          {content || `${title} will appear here...`}
        </div>
      </div>
    );
  };

  const decodedOutputBody = (
    <div style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
      padding: '8px'
    }}>
      <JWTSection title="Header" content={decodedHeader} copyText={decodedHeader} />
      <JWTSection title="Payload" content={decodedPayload} copyText={decodedPayload} />
      <JWTSection title="Signature" content={decodedSignature} copyText={decodedSignature} />
    </div>
  );

  return (
    <>
    <div className="app">
      <Header />
    </div>
    <div className="block-container">
      <Block header="Enter Encoded Value" body={encodedInputBody} />
      <Block header="Decoded Output" body={decodedOutputBody} />
    </div>
    </>
  );
}

export default App
