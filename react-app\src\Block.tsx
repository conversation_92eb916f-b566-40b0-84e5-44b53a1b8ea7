import React, { useState } from "react";

interface BlockProps {
    header: React.ReactNode;
    body: React.ReactNode;
}

export default function Block({header, body}: BlockProps) {
    return (
        <div className="block">
            <div className ="block-header">
                {header}
            </div>
            <div className="block-body">
                {body}
            </div>
        </div>
    );
}