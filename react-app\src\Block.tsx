import React from "react";

interface BlockProps {
    header: string;
    body: React.ReactNode;
    showCopyButton?: boolean;
}

export default function Block({ header, body, showCopyButton = true }: BlockProps) {
    return (
        <div className="block">
            <div className ="block-header">
                {header}
            </div>
            <div className="block-body">
                {body}
                {showCopyButton && (
                    <div className="button">
                        <button>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                            </svg>
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}