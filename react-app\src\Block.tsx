import React from "react";

interface BlockProps {
    header: string;
    body: React.ReactNode;
    showCopyButton?: boolean;
    copyText?: string;
}

export default function Block({ header, body, showCopyButton = true, copyText }: BlockProps) {
    const handleCopy = async () => {
        if (copyText) {
            try {
                await navigator.clipboard.writeText(copyText);
                // Optional: Add visual feedback here (like changing button color briefly)
            } catch (err) {
                console.error('Failed to copy text: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = copyText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
        }
    };

    return (
        <div className="block">
            <div className ="block-header">
                {header}
            </div>
            <div className="block-body">
                {body}
                {showCopyButton && (
                    <div className="button">
                        <button onClick={handleCopy}>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                            </svg>
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}