import React from "react";

interface BlockProps {
    header: string;
    body: React.ReactNode;
}

export default function Block({ header, body }: BlockProps) {
    return (
        <div className="block">
            <div className ="block-header">
                {header}
            </div>
            <div className="block-body">
                {body}
                <div className="button">
                    <button>Copy</button>
                </div>
            </div>
        </div>
    );
}