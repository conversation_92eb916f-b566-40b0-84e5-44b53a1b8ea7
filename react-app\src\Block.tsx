import React from "react";

interface BlockProps {
    header: string;
    body: string;
}

export default function Block({ header, body }: BlockProps) {
    return (
        <div className="block">
            <div className ="block-header">
                {header}
            </div>
            <div className="block-body">
                <textarea>{body}</textarea>
            </div>
        </div>
    );
}