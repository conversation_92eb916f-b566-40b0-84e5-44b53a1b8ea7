import React from 'react';

interface JWTInputProps {
  encodedInput: string;
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

export default function JWTInput({ encodedInput, onInputChange }: JWTInputProps) {
  return (
    <div className="input-container">
      <div className="input-section">
        <div className="input">
          <textarea
            value={encodedInput}
            onChange={onInputChange}
            placeholder="Paste your token here..."
          />
        </div>
      </div>
      
      <div className="signature-section">
        <div className="signature-header">
          <h4>Signature Verification</h4>
          <span className="optional-badge">(Optional)</span>
        </div>
        <div className="signature-input">
          <input
            type="password"
            placeholder="Enter secret key for verification..."
            className="secret-key-input"
          />
        </div>
        <div className="verification-status">
          <div className="status-indicator">
            <span className="status-icon">⚠</span>
            <span className="status-text">Enter secret key to verify</span>
          </div>
        </div>
      </div>
    </div>
  );
}
